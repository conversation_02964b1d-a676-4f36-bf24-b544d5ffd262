// Named Exports
// =============

//     Underscore.js 1.13.7
//     https://underscorejs.org
//     (c) 2009-2024 <PERSON>, <PERSON>, and DocumentCloud and Investigative Reporters & Editors
//     Underscore may be freely distributed under the MIT license.

// Baseline setup.
export { VERSION } from './_setup.js';
export { default as restArguments } from './restArguments.js';

// Object Functions
// ----------------
// Our most fundamental functions operate on any JavaScript object.
// Most functions in Underscore depend on at least one function in this section.

// A group of functions that check the types of core JavaScript values.
// These are often informally referred to as the "isType" functions.
export { default as isObject } from './isObject.js';
export { default as isNull } from './isNull.js';
export { default as isUndefined } from './isUndefined.js';
export { default as isBoolean } from './isBoolean.js';
export { default as isElement } from './isElement.js';
export { default as isString } from './isString.js';
export { default as isNumber } from './isNumber.js';
export { default as isDate } from './isDate.js';
export { default as isRegExp } from './isRegExp.js';
export { default as isError } from './isError.js';
export { default as isSymbol } from './isSymbol.js';
export { default as isArrayBuffer } from './isArrayBuffer.js';
export { default as isDataView } from './isDataView.js';
export { default as isArray } from './isArray.js';
export { default as isFunction } from './isFunction.js';
export { default as isArguments } from './isArguments.js';
export { default as isFinite } from './isFinite.js';
export { default as isNaN } from './isNaN.js';
export { default as isTypedArray } from './isTypedArray.js';
export { default as isEmpty } from './isEmpty.js';
export { default as isMatch } from './isMatch.js';
export { default as isEqual } from './isEqual.js';
export { default as isMap } from './isMap.js';
export { default as isWeakMap } from './isWeakMap.js';
export { default as isSet } from './isSet.js';
export { default as isWeakSet } from './isWeakSet.js';

// Functions that treat an object as a dictionary of key-value pairs.
export { default as keys } from './keys.js';
export { default as allKeys } from './allKeys.js';
export { default as values } from './values.js';
export { default as pairs } from './pairs.js';
export { default as invert } from './invert.js';
export { default as functions,
         default as methods   } from './functions.js';
export { default as extend } from './extend.js';
export { default as extendOwn,
         default as assign    } from './extendOwn.js';
export { default as defaults } from './defaults.js';
export { default as create } from './create.js';
export { default as clone } from './clone.js';
export { default as tap } from './tap.js';
export { default as get } from './get.js';
export { default as has } from './has.js';
export { default as mapObject } from './mapObject.js';

// Utility Functions
// -----------------
// A bit of a grab bag: Predicate-generating functions for use with filters and
// loops, string escaping and templating, create random numbers and unique ids,
// and functions that facilitate Underscore's chaining and iteration conventions.
export { default as identity } from './identity.js';
export { default as constant } from './constant.js';
export { default as noop } from './noop.js';
export { default as toPath } from './toPath.js';
export { default as property } from './property.js';
export { default as propertyOf } from './propertyOf.js';
export { default as matcher,
         default as matches } from './matcher.js';
export { default as times } from './times.js';
export { default as random } from './random.js';
export { default as now } from './now.js';
export { default as escape } from './escape.js';
export { default as unescape } from './unescape.js';
export { default as templateSettings } from './templateSettings.js';
export { default as template } from './template.js';
export { default as result } from './result.js';
export { default as uniqueId } from './uniqueId.js';
export { default as chain } from './chain.js';
export { default as iteratee } from './iteratee.js';

// Function (ahem) Functions
// -------------------------
// These functions take a function as an argument and return a new function
// as the result. Also known as higher-order functions.
export { default as partial } from './partial.js';
export { default as bind } from './bind.js';
export { default as bindAll } from './bindAll.js';
export { default as memoize } from './memoize.js';
export { default as delay } from './delay.js';
export { default as defer } from './defer.js';
export { default as throttle } from './throttle.js';
export { default as debounce } from './debounce.js';
export { default as wrap } from './wrap.js';
export { default as negate } from './negate.js';
export { default as compose } from './compose.js';
export { default as after } from './after.js';
export { default as before } from './before.js';
export { default as once } from './once.js';

// Finders
// -------
// Functions that extract (the position of) a single element from an object
// or array based on some criterion.
export { default as findKey } from './findKey.js';
export { default as findIndex } from './findIndex.js';
export { default as findLastIndex } from './findLastIndex.js';
export { default as sortedIndex } from './sortedIndex.js';
export { default as indexOf } from './indexOf.js';
export { default as lastIndexOf } from './lastIndexOf.js';
export { default as find,
         default as detect } from './find.js';
export { default as findWhere } from './findWhere.js';

// Collection Functions
// --------------------
// Functions that work on any collection of elements: either an array, or
// an object of key-value pairs.
export { default as each,
         default as forEach } from './each.js';
export { default as map,
         default as collect } from './map.js';
export { default as reduce,
         default as foldl,
         default as inject } from './reduce.js';
export { default as reduceRight,
         default as foldr       } from './reduceRight.js';
export { default as filter,
         default as select } from './filter.js';
export { default as reject } from './reject.js';
export { default as every,
         default as all   } from './every.js';
export { default as some,
         default as any  } from './some.js';
export { default as contains,
         default as includes,
         default as include  } from './contains.js';
export { default as invoke } from './invoke.js';
export { default as pluck } from './pluck.js';
export { default as where } from './where.js';
export { default as max } from './max.js';
export { default as min } from './min.js';
export { default as shuffle } from './shuffle.js';
export { default as sample } from './sample.js';
export { default as sortBy } from './sortBy.js';
export { default as groupBy } from './groupBy.js';
export { default as indexBy } from './indexBy.js';
export { default as countBy } from './countBy.js';
export { default as partition } from './partition.js';
export { default as toArray } from './toArray.js';
export { default as size } from './size.js';

// `_.pick` and `_.omit` are actually object functions, but we put
// them here in order to create a more natural reading order in the
// monolithic build as they depend on `_.contains`.
export { default as pick } from './pick.js';
export { default as omit } from './omit.js';

// Array Functions
// ---------------
// Functions that operate on arrays (and array-likes) only, because they’re
// expressed in terms of operations on an ordered list of values.
export { default as first,
         default as head,
         default as take  } from './first.js';
export { default as initial } from './initial.js';
export { default as last } from './last.js';
export { default as rest,
         default as tail,
         default as drop } from './rest.js';
export { default as compact } from './compact.js';
export { default as flatten } from './flatten.js';
export { default as without } from './without.js';
export { default as uniq,
         default as unique } from './uniq.js';
export { default as union } from './union.js';
export { default as intersection } from './intersection.js';
export { default as difference } from './difference.js';
export { default as unzip,
         default as transpose } from './unzip.js';
export { default as zip } from './zip.js';
export { default as object } from './object.js';
export { default as range } from './range.js';
export { default as chunk } from './chunk.js';

// OOP
// ---
// These modules support the "object-oriented" calling style. See also
// `underscore.js` and `index-default.js`.
export { default as mixin } from './mixin.js';
export { default } from './underscore-array-methods.js';
