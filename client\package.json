{"name": "nationalscore-client", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@mui/x-date-pickers": "^8.2.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "apexcharts": "^4.7.0", "axios": "^1.4.0", "date-fns": "^4.1.0", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.1", "web-vitals": "^3.3.1"}, "devDependencies": {"@vitejs/plugin-react": "^4.0.0", "concurrently": "^9.1.2", "vite": "^4.3.9"}, "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}