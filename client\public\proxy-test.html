<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proxy Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 15px;
            background-color: #4a148c;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .result {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Proxy Test</h1>
    <p>This page tests the proxy endpoint to verify it's working correctly.</p>
    
    <form id="proxyForm">
        <div class="form-group">
            <label for="proxyUrl">Proxy URL:</label>
            <input type="text" id="proxyUrl" name="proxyUrl" value="/api/proxy" required>
        </div>
        <div class="form-group">
            <label for="targetUrl">Target URL:</label>
            <input type="text" id="targetUrl" name="targetUrl" value="https://nationalscore.vercel.app/api/health" required>
        </div>
        <div class="form-group">
            <label for="method">Method:</label>
            <select id="method" name="method">
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="OPTIONS">OPTIONS</option>
            </select>
        </div>
        <div class="form-group">
            <label for="body">Request Body (JSON):</label>
            <textarea id="body" name="body" rows="5"></textarea>
        </div>
        <button type="submit">Test Proxy</button>
    </form>
    
    <div class="result">
        <h3>Result:</h3>
        <pre id="result">Submit the form to test the proxy...</pre>
    </div>

    <script>
        const resultElement = document.getElementById('result');
        const proxyForm = document.getElementById('proxyForm');
        
        proxyForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const proxyUrl = document.getElementById('proxyUrl').value;
            const targetUrl = document.getElementById('targetUrl').value;
            const method = document.getElementById('method').value;
            const bodyText = document.getElementById('body').value;
            
            resultElement.textContent = 'Testing proxy...';
            
            try {
                // Prepare request options
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                // Add body for POST requests
                if (method === 'POST' && bodyText.trim()) {
                    try {
                        options.body = JSON.stringify(JSON.parse(bodyText));
                    } catch (e) {
                        resultElement.textContent = `Error parsing JSON body: ${e.message}`;
                        return;
                    }
                }
                
                // Make the request
                const fullUrl = `${proxyUrl}?url=${encodeURIComponent(targetUrl)}`;
                const response = await fetch(fullUrl, options);
                
                // Get response as text first
                const responseText = await response.text();
                
                // Try to parse as JSON
                let responseData;
                try {
                    responseData = JSON.parse(responseText);
                    resultElement.textContent = `Status: ${response.status}\n\n${JSON.stringify(responseData, null, 2)}`;
                } catch (e) {
                    // If not JSON, show as text
                    resultElement.textContent = `Status: ${response.status}\n\n${responseText}`;
                }
            } catch (error) {
                resultElement.textContent = `Error: ${error.message}`;
                console.error('Proxy test error:', error);
            }
        });
    </script>
</body>
</html>
