# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=<PERSON><PERSON><PERSON>
next.title=Omuko Oguddako

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=ku {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=Zimbulukusa
zoom_out_label=Zimbulukusa
zoom_in.title=Funza Munda
zoom_in_label=Funza Munda
zoom.title=Gezzamu
open_file.title=Bikula Fayiro
open_file_label=Ggulawo
print.title=Fulumya
print_label=Fulumya
download.title=Tikula
download_label=Tikula
bookmark.title=Endabika eriwo (koppa oba gulawo mu diriisa epya)
bookmark_label=Endabika Eriwo

# Secondary toolbar and context menu


# Document properties dialog box
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
document_outline_label=Ensalo ze Ekiwandiko
thumbs.title=Laga Ekifanyi Mubufunze
thumbs_label=Ekifanyi Mubufunze

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Omuko {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Ekifananyi kyo Omuko Mubufunze {{page}}

# Find panel button title and messages
find_previous.title=Zuula awayise mukweddamu mumiteddera
find_next.title=Zuula ekidako mukweddamu mumiteddera
find_highlight=Londa byonna
find_not_found=Emiteddera tezuuliddwa

# Error panel labels
error_more_info=Ebisingawo
error_less_info=Mubumpimpi
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Obubaaka: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Ebipangiddwa: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Fayiro {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Layini: {{line}}
rendering_error=Wabadewo ensobi muku tekawo omuko.

# Predefined zoom values
page_scale_width=Obugazi bwo Omuko
page_scale_fit=Okutuka kwo Omuko
page_scale_auto=Okwefunza no Kwegeza
page_scale_actual=Obunene Obutufu
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error_indicator=Ensobi
loading_error=Wabadewo ensobi mukutika PDF.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Enyonyola]
password_ok=OK

printing_not_supported=Okulaabula: Okulumya empapula tekuwagirwa enonyeso enno.
