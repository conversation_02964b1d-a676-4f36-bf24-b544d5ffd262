{"name": "server", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "0.24.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.0", "mongoose": "^8.14.0", "multer": "^1.4.5-lts.2", "pdf-parse": "^1.1.1"}, "devDependencies": {"nodemon": "^3.1.10"}}