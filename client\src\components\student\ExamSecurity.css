/* Exam Security CSS */

/* Prevent text selection */
.exam-secure-content {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
}

/* Allow text selection only in input fields */
.exam-secure-content input,
.exam-secure-content textarea {
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
  user-select: auto !important;
  -webkit-touch-callout: auto !important;
  -khtml-user-select: auto !important;
}

/* Styles for the warning overlay */
.exam-warning-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(220, 53, 69, 0.95);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
  padding: 2rem;
}

.exam-warning-icon {
  font-size: 5rem;
  margin-bottom: 1rem;
}

.exam-warning-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.exam-warning-message {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  max-width: 600px;
}

.exam-warning-button {
  background-color: white;
  color: #dc3545;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: bold;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.exam-warning-button:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Fullscreen mode styles */
.exam-fullscreen-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 1000;
  overflow-y: auto;
}

/* Pulse animation for warning elements */
@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

.exam-warning-pulse {
  animation: pulse-warning 1.5s infinite;
}
