<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="512" height="512" rx="100" fill="url(#paint0_linear)" />
  
  <!-- Graduation Cap -->
  <path d="M256 120L96 200L256 280L416 200L256 120Z" fill="white" />
  <path d="M176 230V310C176 330 212 350 256 350C300 350 336 330 336 310V230" stroke="white" stroke-width="16" stroke-linecap="round" />
  <path d="M416 200V280" stroke="white" stroke-width="16" stroke-linecap="round" />
  
  <!-- Test Paper -->
  <rect x="176" y="180" width="160" height="200" rx="8" fill="#4A148C" stroke="white" stroke-width="8" />
  <line x1="200" y1="220" x2="312" y2="220" stroke="white" stroke-width="8" stroke-linecap="round" />
  <line x1="200" y1="260" x2="312" y2="260" stroke="white" stroke-width="8" stroke-linecap="round" />
  <line x1="200" y1="300" x2="280" y2="300" stroke="white" stroke-width="8" stroke-linecap="round" />
  
  <!-- Letter T for Testify -->
  <path d="M256 120L256 380" stroke="white" stroke-width="24" stroke-linecap="round" />
  <path d="M196 140L316 140" stroke="white" stroke-width="24" stroke-linecap="round" />
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="512" y2="512" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4A148C" />
      <stop offset="1" stop-color="#7C43BD" />
    </linearGradient>
  </defs>
</svg>
